[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "novel-parser"
version = "0.2.0"
description = "A modern, high-performance system for parsing and monitoring TXT and EPUB novel files with byte-offset indexing and incremental parsing"
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.12"
authors = [
    {name = "Novel Parser Team"},
]
keywords = ["novel", "parser", "epub", "txt", "fastapi", "monitoring"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Text Processing :: Markup :: HTML",
]

dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "watchdog>=3.0.0",
    "ebooklib>=0.18.0",
    "beautifulsoup4>=4.12.2",
    "pydantic>=2.0.0",
    "python-multipart>=0.0.6",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
]

[project.urls]
Homepage = "https://github.com/novel-parser/novel-parser"
Repository = "https://github.com/novel-parser/novel-parser"
Documentation = "https://novel-parser.readthedocs.io"
Issues = "https://github.com/novel-parser/novel-parser/issues"

[project.scripts]
novel-parser = "novel_parser.main:main"

[tool.hatch.build.targets.wheel]
packages = ["src/novel_parser"]

[tool.hatch.build.targets.sdist]
include = [
    "/src",
    "/README.md",
    "/pyproject.toml",
]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["novel_parser"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=novel_parser",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
